<template>
    <div class="action-button-group">
        <Button
            v-for="action in actions"
            :key="action.key || action.label"
            class="action-btn border"
            size="small"
            @click="handleAction(action)"
            :disabled="action.disabled"
        >
            {{ action.label }}
            <i v-if="action.hasDropdown" class="icon2017 icon-arrow_01_down"></i>
        </Button>
    </div>
</template>

<script>
import { components } from "common";

const { Button } = components;

export default {
    name: "ActionButtonGroup",
    langModule: "key-management",
    components: {
        But<PERSON>
    },
    props: {
        actions: {
            type: Array,
            default: () => []
        }
    },
    methods: {
        handleAction(action) {
            this.$emit("action", action);
        }
    }
};
</script>

<style lang="less" scoped>
.action-button-group {
    display: flex;
    justify-content: stretch;
    align-items: stretch;
    gap: 10px;

    .action-btn {
        flex: 1;
        height: auto;
        min-width: auto;
        padding: 5px 15px;
        border-radius: 30px;
        border: 1px solid #e1e2e6;
        background: #ffffff;

        font-family: SF Pro;
        font-weight: 400;
        font-size: 14px;
        line-height: 1.43em;
        text-align: center;
        color: #515666;

        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;

        i {
            font-size: 12px;
            color: #a0a3af;
        }

        &:hover {
            box-shadow: 0px 1px 3px rgba(0, 10, 30, 0.1);
        }
    }
}
</style>
