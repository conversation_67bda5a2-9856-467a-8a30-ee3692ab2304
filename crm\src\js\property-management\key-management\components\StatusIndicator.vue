<template>
    <div class="status-indicator">
        <span
            class="status-dot"
            :class="statusClass"
        ></span>
        <span class="status-text">{{ statusText }}</span>
    </div>
</template>

<script>
export default {
    name: "StatusIndicator",
    langModule: "key-management",
    props: {
        status: {
            type: String,
            required: true
        },
        statusConfig: {
            type: Object,
            default: () => ({
                available: { class: "status-available", text: "Available" },
                checked_out: { class: "status-checked-out", text: "Checked Out" },
                lost: { class: "status-lost", text: "Lost" },
                archived: { class: "status-archived", text: "Archived" }
            })
        }
    },
    computed: {
        statusClass() {
            return this.statusConfig[this.status]?.class || "";
        },
        statusText() {
            return this.statusConfig[this.status]?.text || this.status;
        }
    }
};
</script>

<style lang="less" scoped>
.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        opacity: 0.8;

        &.status-available {
            background: #20c472;
        }
        &.status-checked-out {
            background: #5d51e2;
        }
        &.status-lost {
            background: #f0454c;
        }
        &.status-archived {
            background: #c6c8d1;
        }
    }

    .status-text {
        font-family: SF Pro Text;
        font-weight: 400;
        font-size: 12px;
        line-height: 1.67em;
        color: #515666;
    }
}
</style>
