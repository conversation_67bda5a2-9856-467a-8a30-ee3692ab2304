<template>
    <div class="note-panel-container">
        <!-- 简单模式 -->
        <div class="note-panel-simple" v-show="showSimple">
            <Select
                :isOpen.sync="isDropdownOpen"
                class="note-type-select"
                @datachange="onTypeSelectChange"
                :noBorder="true"
                :dataSource="noteTypes"
                :value="currentType"
                dropdownCls="note-type-dropdown"
            ></Select>
            <div class="note-input-area">
                <input
                    class="chime-input large"
                    type="text"
                    :placeholder="$t('activities.addNote')"
                    @click="expandToComplexMode"
                />
            </div>
        </div>

        <!-- 复杂模式 -->
        <div class="note-panel-complex" v-show="!showSimple">
            <ul class="note-type-tabs">
                <li
                    v-for="(item, index) in noteTypes"
                    :key="index + 'noteType'"
                    :class="currentType === item.id ? 'active' : ''"
                    @click="changeType(item.id)"
                >
                    {{ item.name }}
                </li>
            </ul>
            <div class="note-content" :class="currentType">
                <PropertyNoteInput
                    :showSimple="showSimple"
                    :autofocus="true"
                    :currentType="currentType"
                    :sendContent="sendContent"
                    :propertyId="propertyId"
                    @getContent="addNote"
                    ref="noteInput"
                ></PropertyNoteInput>
                <div class="bottom-actions">
                    <CheckBox
                        class="pin-top"
                        :label="$t('activities.pinToPTop')"
                        :checked="pinToTop"
                        @change="onPinToTopChange"
                    ></CheckBox>
                    <div class="action-buttons">
                        <span class="chime-btn invisible" @click="collapseToSimpleMode">
                            {{ $st("common", "popWin.cancelText") }}
                        </span>
                        <span
                            class="chime-btn primary"
                            @click="submitNote"
                            :class="{ disabled: isSubmitting }"
                        >
                            {{ addBtnText }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { components, utils } from "common";
import { track, localStore } from "crm";
import PropertyNoteInput from "./PropertyNoteInput.vue";
import api from "../api/index.js";

const { Select, CheckBox } = components;

export default {
    name: "NotePanel",
    langModule: "listingDetail",

    components: {
        Select,
        CheckBox,
        PropertyNoteInput
    },

    props: {
        propertyId: {
            type: [String, Number],
            required: true
        }
    },

    data() {
        return {
            currentType: 16, // 默认为笔记类型
            isDropdownOpen: false,
            showSimple: true,
            sendContent: false,
            isSubmitting: false,
            pinToTop: false,
            noteTypes: [
                {
                    id: 16,
                    name: this.getTranslation("activities.note", "Note"),
                    ga: "PropertyDetail_Activities_Add_Note",
                    addBtnText: this.getTranslation("activities.addNoteText", "Add Note")
                },
                {
                    id: 25,
                    name: this.getTranslation("activities.logCall", "Log Call"),
                    ga: "PropertyDetail_Activities_Add_Call",
                    addBtnText: this.getTranslation("activities.logCall", "Log Call")
                },
                {
                    id: 7,
                    name: this.getTranslation("activities.logEmail", "Log Email"),
                    ga: "PropertyDetail_Activities_Add_Email",
                    addBtnText: this.getTranslation("activities.logEmail", "Log Email")
                }
            ]
        };
    },

    computed: {
        addBtnText() {
            const currentNoteType = this.noteTypes.find(item => item.id === this.currentType);
            return currentNoteType ? currentNoteType.addBtnText : this.getTranslation("activities.addNoteText", "Add Note");
        }
    },

    watch: {
        showSimple(val) {
            if (!val) {
                // 使用 nextTick 确保 DOM 更新后再聚焦
                this.$nextTick(() => {
                    try {
                        this.$refs.noteInput?.focus();
                    } catch (error) {
                        console.log("Focus error:", error);
                    }
                });
            }
        }
    },

    mounted() {
        // 从本地存储恢复笔记类型
        const savedNoteType = localStore.getLocal("propertyNoteTypeId");
        if (savedNoteType && savedNoteType !== "undefined") {
            this.currentType = parseInt(savedNoteType);
        }
    },

    methods: {
        getTranslation(key, fallback) {
            try {
                return this.$t(key);
            } catch (error) {
                console.warn(`Translation key "${key}" not found, using fallback: "${fallback}"`);
                return fallback;
            }
        },

        expandToComplexMode() {
            this.showSimple = false;
        },

        collapseToSimpleMode() {
            this.showSimple = true;
        },

        changeType(type) {
            this.currentType = type;
            localStore.setLocal("propertyNoteTypeId", type);
        },

        onTypeSelectChange(res) {
            this.changeType(res.value);
            this.expandToComplexMode();
        },

        onPinToTopChange(event) {
            this.pinToTop = event.checked;
            track.trackGa.sendEvent("PropertyDetail_Activities_Pin_Top");
        },

        submitNote() {
            const { valid, tip } = this.$refs.noteInput.checkContentLen();

            if (valid) {
                this.sendContent = true;
            } else {
                tip && utils.toast({
                    content: tip
                });
            }
        },

        async addNote(res) {
            const currentNoteType = this.noteTypes.find(item => item.id === this.currentType);
            if (currentNoteType) {
                track.trackGa.sendEvent(currentNoteType.ga);
            }

            const content = res.content;

            // 验证内容
            if (!content && this.currentType === 16) {
                utils.toast({
                    content: this.getTranslation("activities.contentNoEmpty", "Content cannot be empty"),
                    time: 1000
                });
                this.sendContent = false;
                return;
            }

            if (this.isSubmitting) {
                return;
            }

            this.isSubmitting = true;

            const params = {
                content: content,
                type: this.currentType,
                propertyId: this.propertyId,
                pinToTop: this.pinToTop,
                timestamp: Date.now()
            };

            try {
                // 这里应该调用实际的API接口
                await this.savePropertyNote(params);

                // 清空输入内容
                this.$refs.noteInput.clearContent();

                // 重置状态
                this.sendContent = false;
                this.isSubmitting = false;

                // 触发事件通知父组件更新时间线
                this.$emit("note-added", params);

                // 显示成功提示
                utils.toast({
                    content: this.getTranslation("activities.addSuccess", "Added successfully"),
                    time: 1000
                });

            } catch (error) {
                console.error("添加笔记失败:", error);
                this.sendContent = false;
                this.isSubmitting = false;

                utils.toast({
                    content: this.getTranslation("activities.addFailed", "Failed to add"),
                    time: 1000
                });
            }
        },

        async savePropertyNote(params) {
            try {
                // 使用真实的API接口
                const response = await api.addPropertyNote(params);
                return response;
            } catch (error) {
                // 如果API不可用，使用模拟数据作为降级方案
                console.warn("API调用失败，使用模拟数据:", error);
                return new Promise((resolve, reject) => {
                    setTimeout(() => {
                        if (Math.random() > 0.1) { // 90% 成功率
                            resolve({ success: true, data: params });
                        } else {
                            reject(new Error("网络错误"));
                        }
                    }, 500);
                });
            }
        }
    }
};
</script>

<style lang="less" scoped>
* {
    box-sizing: border-box;
}

.note-panel-container {
    border: 1px solid #ebecf1;
    border-radius: 6px;
    background: #fff;

    .note-panel-simple {
        height: 50px;
        line-height: 50px;
        display: flex;

        .note-type-select {
            width: 136px;
            font-size: 14px;

            :deep(.com-dropdown-label) {
                padding-left: 20px;
                width: 136px;

                .com-dropdown-text {
                    text-align: center;
                    font-size: 16px;
                    color: #515666;
                    font-weight: 500;
                }

                .icon.right {
                    right: 20px;
                }
            }
        }

        .note-input-area {
            flex: 1;
            margin-right: 20px;

            input {
                width: 100%;
                box-sizing: border-box;
            }
        }
    }

    .note-panel-complex {
        border-radius: 4px;
        background: #fff;
        display: flex;

        .note-type-tabs {
            background-color: #f6f7fb;
            width: 116px;
            padding: 18px 0 20px;
            border-right: 1px solid #ebecf1;

            li {
                height: 40px;
                line-height: 40px;
                margin-top: 2px;
                padding: 0 20px;
                font-size: 16px;
                font-weight: 500;
                color: #515666;
                text-align: left;
                cursor: pointer;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &.active {
                    background-color: #fff;
                    border-top: 1px solid #ebecf1;
                    border-bottom: 1px solid #ebecf1;
                    height: 38px;
                    line-height: 38px;
                    color: var(--primary-color);
                    margin-right: -1px;
                }
            }
        }

        .note-content {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .bottom-actions {
            padding-top: 16px;
            display: flex;
            align-items: center;
            font-size: 12px;
            color: #515666;

            .pin-top {
                margin-right: 16px;
            }

            .action-buttons {
                height: 36px;
                line-height: 36px;
                font-size: 0; // remove space
                flex: 1;
                text-align: right;

                .chime-btn {
                    height: 36px;
                    line-height: 36px;

                    + .chime-btn {
                        margin-left: 10px;
                    }
                }
            }
        }
    }
}
</style>

<style lang="less">
.note-type-dropdown {
    width: 150px;
    position: relative;

    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        width: 14px;
        height: 14px;
        background-color: #fff;
    }

    .com-select-item {
        padding-left: 20px;
    }
}
</style>
