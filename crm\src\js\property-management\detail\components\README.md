# Property Management Detail Components

## NotePanel 组件

### 概述
`NotePanel` 组件是基于 `timeline-top.vue` 重构和优化的笔记面板组件，专门用于房产管理详情页面。该组件提供了简洁的笔记添加功能，支持多种类型的活动记录。

### 主要功能
- **双模式界面**: 支持简单模式和复杂模式切换
- **多种笔记类型**: 支持笔记、通话记录、邮件记录等类型
- **富文本编辑**: 集成了富文本编辑器，支持格式化内容
- **置顶功能**: 支持将重要笔记置顶显示
- **实时验证**: 内容长度验证和必填项检查
- **国际化支持**: 完整的多语言支持，带有降级机制

### 重构优化点

#### 1. 代码结构优化
- 移除了与 Lead 相关的复杂逻辑（电话号码选择、联系人信息等）
- 简化了状态管理，专注于笔记功能
- 优化了组件间的通信机制

#### 2. 组件复用
- 复用了项目中现有的 `Select` 和 `CheckBox` 组件
- 基于 `TimelineInput` 创建了专门的 `PropertyNoteInput` 组件
- 复用了 `noteEditor` 富文本编辑器

#### 3. API 设计
- 创建了专门的 API 模块 (`api/index.js`)
- 支持真实 API 调用和模拟数据降级
- 统一的错误处理机制

#### 4. 用户体验优化
- 平滑的模式切换动画
- 智能的焦点管理
- 友好的错误提示和成功反馈
- 本地存储记忆用户偏好

### 使用方法

```vue
<template>
    <NotePanel 
        :propertyId="propertyId" 
        @note-added="onNoteAdded" 
    />
</template>

<script>
import NotePanel from './components/NotePanel.vue';

export default {
    components: {
        NotePanel
    },
    data() {
        return {
            propertyId: 'your-property-id'
        };
    },
    methods: {
        onNoteAdded(noteData) {
            console.log('Note added:', noteData);
            // 处理笔记添加后的逻辑
        }
    }
};
</script>
```

### Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| propertyId | String/Number | 是 | - | 房产ID |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| note-added | noteData | 笔记添加成功时触发 |

### 笔记类型

| ID | 类型 | 说明 |
|----|------|------|
| 16 | Note | 普通笔记 |
| 25 | Log Call | 通话记录 |
| 7 | Log Email | 邮件记录 |

## PropertyNoteInput 组件

### 概述
`PropertyNoteInput` 是基于 `TimelineInput` 重构的输入组件，专门用于房产笔记的输入和编辑。

### 主要特性
- 富文本编辑支持
- 内容长度验证
- 自动焦点管理
- 响应式设计

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| propertyId | String/Number | - | 房产ID |
| currentType | Number | 16 | 当前笔记类型 |
| showSimple | Boolean | false | 是否显示简单模式 |
| sendContent | Boolean | false | 是否发送内容 |
| placeholder | String | "Add a note..." | 占位符文本 |
| autofocus | Boolean | false | 是否自动聚焦 |
| maxLength | Number | 4000 | 最大字符长度 |

## API 模块

### 接口列表

- `addPropertyNote(params)` - 添加房产笔记
- `getPropertyTimeline(propertyId, options)` - 获取房产时间线
- `updatePropertyNote(noteId, params)` - 更新房产笔记
- `deletePropertyNote(noteId)` - 删除房产笔记
- `getPropertyDetail(propertyId)` - 获取房产详情
- `getPropertyActivityStats(propertyId)` - 获取房产活动统计

## 样式说明

组件使用了 Less 预处理器，支持：
- CSS 变量（如 `--primary-color`）
- 响应式设计
- 深度选择器 (`:deep()`)
- 模块化样式

## 测试

提供了完整的单元测试文件 `__tests__/NotePanel.test.js`，覆盖：
- 组件渲染
- 用户交互
- 状态管理
- API 调用
- 错误处理

运行测试：
```bash
npm test -- NotePanel.test.js
```

## 注意事项

1. **国际化**: 组件包含了翻译降级机制，确保在国际化文件缺失时仍能正常显示
2. **API 降级**: 当真实 API 不可用时，会自动切换到模拟数据模式
3. **性能优化**: 使用了 Vue 的响应式特性和计算属性来优化性能
4. **错误处理**: 完善的错误捕获和用户友好的错误提示

## 未来扩展

- 支持更多笔记类型
- 添加笔记模板功能
- 支持附件上传
- 添加协作功能
- 支持笔记搜索和筛选
