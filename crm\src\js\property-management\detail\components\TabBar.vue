<template>
    <div class="tab-bar">
        <div class="tabs">
            <a
                href="#"
                v-for="tab in tabs"
                :key="tab.key"
                :class="{ active: tab.key === activeTab }"
                @click.prevent="selectTab(tab.key)"
            >
                {{ tab.label }}
                <i v-if="tab.hasDropdown" class="icon2017 icon-arrow_01_down"></i>
            </a>
        </div>
        <div class="right">
            <button class="publish-button">{{ $t("listingDetail.publishToPortals") }}</button>
            <button class="more-button">
                <i class="icon2017 icon-more_01"></i>
            </button>
        </div>
    </div>
</template>

<script>
export default {
    name: "TabBar",
    langModule: "key-management",
    props: {
        activeTab: {
            type: String,
            default: "overview"
        }
    },
    data() {
        return {
            tabs: [
                { key: "overview", label: this.$t("listingDetail.overview") },
                { key: "marketing", label: this.$t("listingDetail.marketing") },
                { key: "keys", label: this.$t("listingDetail.keys") },
                { key: "viewings", label: this.$t("listingDetail.viewings") },
                { key: "buyers", label: this.$t("listingDetail.buyers") },
                { key: "offers", label: this.$t("listingDetail.offers") },
                { key: "sales-progression", label: this.$t("listingDetail.salesProgression") },
                { key: "more", label: this.$t("listingDetail.more"), hasDropdown: true }
            ]
        };
    },
    methods: {
        selectTab(tabKey) {
            this.$emit("tab-change", tabKey);
        }
    }
};
</script>

<style scoped>
.tab-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
    border-bottom: 1px solid #e1e2e6;
}
.tabs {
    display: flex;
    align-items: center;
    gap: 30px;
}
.tabs a {
    text-decoration: none;
    color: #797e8b;
    font-size: 14px;
    font-weight: 510;
    padding: 10px 0;
}
.tabs a.active {
    color: #5d51e2;
    border-bottom: 2px solid #5d51e2;
}
.right {
    display: flex;
    align-items: center;
    gap: 10px;
}
.publish-button {
    background-color: #5d51e2;
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 20px;
    font-size: 14px;
    cursor: pointer;
}
.more-button {
    background-color: #fff;
    border: 1px solid #e1e2e6;
    border-radius: 6px;
    padding: 8px;
    cursor: pointer;
}
</style>
