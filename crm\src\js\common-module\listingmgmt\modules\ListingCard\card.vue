<template>
    <div
        class="listing-cardview-card"
        v-lazy="{
            load: loadMatchLead,
            loadParam: {
                source: listing,
                changeEl: false
            }
        }"
    >
        <div class="head-img" @click="onClick('manageListing')">
            <img v-lazyload="imgConfig(listing)" />

            <div class="tag-box">
                <span class="listing-tag" v-for="t in tags" :key="t.label" :class="t.cls">
                    <i v-if="t.icon" :class="t.icon" />{{ t.label }}
                </span>
            </div>
        </div>
        <div class="info-box">
            <template v-if="!isMyListingForExpired">
                <div class="performance" v-if="listing.listingStatus !== 'Sold'">
                    <span
                        id="view_icon"
                        class="p-item can-not-click"
                        v-if="isShowListingViews"
                        v-tip="{
                            content: tipContent.views(getCurCardNums('listingViews'))
                        }"
                    >
                        <i class="icon2017 icon-view_01" />
                        {{ getCurCardNums("listingViews") }}
                    </span>
                    <span
                        id="leads_icon"
                        class="p-item can-not-click"
                        v-tip="{
                            content: tipContent.leads(getCurCardNums('leads'))
                        }"
                    >
                        <i class="icon2017 icon-favorite_01" />
                        {{ getCurCardNums("leads") }}
                    </span>
                    <span
                        id="showings_icon"
                        class="p-item can-not-click"
                        v-tip="{
                            content: tipContent.showings(getCurCardNums('showings'))
                        }"
                    >
                        <i class="icon2017 icon-calendar_05" />
                        {{ getCurCardNums("showings") }}
                    </span>
                    <span
                        id="matchlead_icon"
                        class="p-item can-not-click"
                        v-tip="{
                            content: tipContent.matchLeads(listing.leadCount)
                        }"
                    >
                        <i class="icon2017 icon-people_03" />
                        {{ listing.leadCount || 0 }}
                    </span>
                </div>
                <!--  sold  -->
                <div class="performance" v-else>
                    <span
                        id="view_icon"
                        class="p-item can-not-click"
                        v-if="getCurCardNums('listingViews') !== -1"
                        v-tip="{
                            content: tipContent.views(getCurCardNums('listingViews'))
                        }"
                    >
                        <i class="icon2017 icon-view_01" />
                        {{ getCurCardNums("listingViews") }}
                    </span>
                    <span
                        id="leads_icon"
                        class="p-item can-not-click"
                        v-tip="{
                            content: tipContent.leads(getCurCardNums('leads'))
                        }"
                    >
                        <i class="icon2017 icon-favorite_01" />
                        {{ getCurCardNums("leads") }}
                    </span>
                    <span
                        id="showings_icon"
                        class="p-item can-not-click"
                        v-tip="{
                            content: tipContent.showings(getCurCardNums('showings'))
                        }"
                    >
                        <i class="icon2017 icon-calendar_05" />
                        {{ getCurCardNums("showings") }}
                    </span>
                </div></template
            >
            <div class="price-line">
                <span style="cursor: pointer" @click="onClick('manageListing')">{{
                    getListingPrice(listing)
                }}</span>
                <ListingStatusTag v-if="!isUnpublishedRls" :listingStatus="listing.listingStatus" />
            </div>
            <div class="address-line" @click="onClick('manageListing')">
                {{ listing.address }}
            </div>
            <button
                v-if="!isMyListingForExpired"
                class="mng-listing-bt"
                @click="onClick('manageListing')"
            >
                {{ $t("listingMgmt.litingCard.btn1") }}
            </button>
            <button v-else class="mng-listing-bt" @click="handleEditListing">
                {{ $t("listingMgmt.litingCard.editCard") }}
            </button>
        </div>
    </div>
</template>

<script>
import { formats, globalization, utils } from "common";
import { basePermission } from "crm";
import { useIsUK, useCurrencySymbol } from "@/hooks";
import { addSinglePropertyPromotion } from "@/js/common-module/landingPage/add";
import { listingTools } from "@/js/smart-listing/smartDashBoard/constant";
import MixinBuyerCompensation from "@/js/listing-detail/buyerCompensation/index.js";
import mixinPocket from "@/js/listing-detail/pocketListing.js";
import ListingStatusTag from "@/js/common-module/listingMap/listingStatusTag.vue";
const defHouseImg =
    "https://fs01.chime.me/image/fs/sitebuild/2021224/20/original_ddff9285-b8a4-4fd5-840f-0233eb9cc979.png";
export default {
    props: {
        listing: {
            type: Object,
            default: () => ({})
        },
        scrollSelector: {
            type: String,
            default: ""
        },
        loadMatchLead: {
            type: Function,
            default: () => {
                return function () {};
            }
        },
        soldRelativeActiveListing: {
            type: Array,
            default: () => []
        },
        allActiveNums: {
            type: Array,
            default: () => []
        },
        isShowSocialEntry: {
            type: Boolean,
            default: () => true
        },
        hasPostChannelAuth: {
            type: Boolean,
            default: () => false
        },
        isShowVideoEntry: {
            type: Boolean,
            default: false
        },
        hasListingPromotion: {
            type: Boolean,
            default: false
        },
        adTagText: {
            type: String,
            default: () => globalization.t("listingMgmt.litingCard.adTag")
        },
        adNoticeText: {
            type: String,
            default: () => globalization.t("listingMgmt.litingCard.adNotice")
        },
        targetBuyerLabel: {
            type: String,
            default: ""
        },
        domainSiteId: {
            type: Number,
            default: null
        },
        isMyListingForExpired: {
            type: Boolean,
            default: false
        }
    },
    mixins: [MixinBuyerCompensation, mixinPocket],
    data() {
        this.imgDefault = defHouseImg;
        this.tools = listingTools();
        this.tipContent = {
            views(count) {
                const text =
                    count > 0
                        ? globalization.t("listingMgmt.litingCard.viewTip1", {
                              count
                          })
                        : globalization.t("listingMgmt.litingCard.viewTip2", {
                              count
                          });
                if (this.isUK) {
                    return utils.localize.listingToUK(text);
                }
                return text;
            },
            leads(count) {
                const text =
                    count > 0
                        ? globalization.t("listingMgmt.litingCard.leadTip1", {
                              count
                          })
                        : globalization.t("listingMgmt.litingCard.leadTip2", {
                              count
                          });
                if (this.isUK) {
                    return utils.localize.listingToUK(text);
                }
                return text;
            },
            showings(count) {
                const text =
                    count > 0
                        ? globalization.t("listingMgmt.litingCard.showTip1", {
                              count
                          })
                        : globalization.t("listingMgmt.litingCard.showTip2", {
                              count
                          });
                if (this.isUK) {
                    return utils.localize.listingToUK(text);
                }
                return text;
            },
            matchLeads(count) {
                const text =
                    count > 0
                        ? globalization.t("listingMgmt.litingCard.matchLeadTip1", {
                              count
                          })
                        : globalization.t("listingMgmt.litingCard.matchLeadTip2", {
                              count
                          });
                if (this.isUK) {
                    return utils.localize.listingToUK(text);
                }
                return text;
            }
        };
        return {};
    },
    setup() {
        const isUK = useIsUK();
        const currencySymbol = useCurrencySymbol();
        return {
            isUK,
            currencySymbol
        };
    },
    computed: {
        getAdNoticeText() {
            const text = this.adNoticeText || globalization.t("listingMgmt.litingCard.adNotice");
            if (this.isUK) {
                return utils.localize.listingToUK(text);
            }
            return text;
        },
        price() {
            return this.listing.price;
        },
        location() {
            const { address, streetAddress, city, state, zipCode } = this.listing;
            return address
                ? address
                : [streetAddress, city, [state, zipCode].filter(Boolean).join(" ")]
                      .filter(Boolean)
                      .join(", ");
        },
        isUnpublishedRls({ listing, isNoRlsDraftListing }) {
            return listing?.rlsPublishStatus === 0 || isNoRlsDraftListing;
        },
        isNoRls({ listing }) {
            return listing?.multiFieldsJson?.isNotRLSAddress;
        },
        isNoRlsDraftListing({ isNoRls, listing }) {
            return isNoRls && listing?.rlsPublishStatus === 0;
        },
        isRLSPermission() {
            return basePermission.rls;
        },
        tags({ isRLSPermission, listing, isNoRls }) {
            let ret = [];
            if (listing?.rlsPublishStatus === 1) {
                if (!isNoRls) {
                    ret.push({
                        label: this.$t("listingMgmt.litingCard.rls"),
                        icon: "icon2017 icon-success_01"
                    });
                }
                if (listing?.multiFieldsJson?.chimeLuxTV) {
                    ret.push({
                        label: this.$t("listingMgmt.litingCard.luxVT"),
                        icon: "icon2017 icon-success_01"
                    });
                }
            }
            if (!isNoRls && listing?.rlsPublishStatus === 0) {
                ret.push({
                    label: this.$t("listingMgmt.litingCard.unpublished"),
                    icon: "icon2017 icon-error"
                });
            }
            if ((listing?.tags || []).find((t) => t == "pocket-listing") && !isRLSPermission) {
                ret.push({
                    label: this.$t("listingMgmt.litingCard.pocketListing")
                });
            }
            if (
                isRLSPermission &&
                listing?.multiFieldsJson?.chimePermissions === "Participant Only Listing"
            ) {
                ret.push({
                    label: this.$t("listingMgmt.litingCard.participantOnly"),
                    icon: "icon2017 icon-success_01"
                });
            }

            if (isNoRls && listing?.mlsOrgId == 22000 && isRLSPermission) {
                if (listing?.rlsPublishStatus === 1) {
                    ret.push({
                        label: this.$t("listingMgmt.litingCard.nonRlsListing"),
                        icon: "icon2017 icon-success_01 color-gray"
                    });
                } else {
                    ret.push({
                        label: this.$t("listingMgmt.litingCard.nonRls"),
                        icon: "icon2017 icon-error"
                    });
                }
            }
            // //? Add here other tag conditions for UK
            // if (+this.listing.publishFlag === 0) {
            //     ret.push({ label: "Unpublished" });
            // }
            if (+this.listing.draftFlag === 1) {
                ret.push({ label: "Draft" });
            }
            ret.unshift({
                label: listing.purchaseType,
                cls: listing.purchaseType.replace(/ /g, "-").toLowerCase()
            });
            return ret;
        },
        listingTab() {
            //  redirect to smart listing  page time ， Need to distinguish sold or on sale status
            return this.isSold ? "Archived" : "OnMarket";
        },
        getTools({ isShowSocialEntry, isShowVideoEntry, listing }) {
            const { Dialer, showDesignCenter } = basePermission;
            return listingTools().filter((item) => {
                const { key } = item;

                //  Judgment of authority
                if (key === "socialPosts") {
                    return !!isShowSocialEntry;
                } else if (key === "textCode") {
                    return !!Dialer;
                } else if (key === "autoVideo") {
                    return isShowVideoEntry && listing.listingStatus !== "Sold";
                } else if (key === "design") {
                    return showDesignCenter;
                } else {
                    return true;
                }
            });
        },
        asyncData() {
            let listingId = +this.listing?.id;
            return this.allActiveNums.find((item) => +item.listingId === +listingId) || {};
        },
        isSold() {
            return ["Sold", "Let"].includes(this.listing.listingStatus);
        },
        isShowListingViews() {
            if (__IS_UK__) {
                return true;
            }
            return !this.isSold && this.listing?.chimeFirstInserted >= this.asyncData?.releaseTime;
        }
    },
    methods: {
        handleEditListing() {
            this.editPocket(
                { ...(this.listing || {}), isRLSPermission: this.isRLSPermission },
                window.location.href,
                () => {
                    console.log("success");
                },
                {
                    isMyListing: true,
                    domainSiteId: this.domainSiteId
                }
            );
        },
        buyerCompensationTip(buyerAmount) {
            return buyerAmount
                ? this.$t("listingMgmt.litingCard.buyerCompTip2")
                : this.$t("listingMgmt.litingCard.buyerCompTip1");
        },
        handleClickIcon(tool) {
            const { name } = tool;
            const status = this.toolStatus(tool);
            const listingId = this.listing.id;
            // todo， sold Associated active There is no corresponding data for the listing ： not clickable
            if (status === "disabled") {
                return;
            }
            if (status === "active") {
                if (name === "autoVideo" || name === "designs") {
                    this.goDesignTab({ name, listingId, isOpenAdd: false });
                    return;
                }
                window.__crmRouter.push({
                    path: "/smartlisting",
                    query: {
                        id: listingId,
                        tab: name,
                        status: this.listingTab,
                        siteId: this.domainSiteId,
                        listingStatus: this.listing.listingStatus
                    }
                });
            } else if (name === "landingPage") {
                addSinglePropertyPromotion(this.listing, this.domainSiteId);
            } else {
                if (name === "autoVideo" || name === "designs") {
                    this.goDesignTab({ name, listingId, isOpenAdd: true });
                    return;
                }
                //  other unlit tool redirect to smart listing page ， and open the corresponding add bullet box
                window.__crmRouter.push({
                    path: "/smartlisting",
                    query: {
                        id: listingId,
                        tab: name,
                        openAdd: name,
                        status: this.listingTab,
                        siteId: this.domainSiteId,
                        listingStatus: this.listing.listingStatus
                    }
                });
            }
        },
        goDesignTab({ name, listingId, isOpenAdd = false }) {
            const type = name === "autoVideo" ? "autoVideo" : "designs";
            const query = {
                id: listingId,
                tab: "designs",
                type,
                status: this.listingTab,
                siteId: this.domainSiteId,
                listingStatus: this.listing.listingStatus
            };
            if (isOpenAdd) {
                query.openAdd = "designs";
            }
            window.__crmRouter.push({
                path: "/smartlisting",
                query
            });
        },
        toolTipContent(tool) {
            if (this.isUK) {
                tool.label = utils.localize.listingToUK(tool.label);
            }
            const { label } = tool;
            const status = this.toolStatus(tool);
            const isActive = status === "active";
            const isDisabled = status === "disabled";
            return isActive
                ? this.$t("listingMgmt.litingCard.toolTip1", { label })
                : isDisabled
                ? this.$t("listingMgmt.litingCard.toolTip2")
                : this.$t("listingMgmt.litingCard.toolTip3", { label });
        },
        showMatchedLeads(navValue) {
            const listingId = this.listing.id;
            window.__crmRouter.push({
                path: "/smartlisting",
                query: {
                    id: listingId,
                    openMatchLead: navValue,
                    status: this.listingTab,
                    siteId: this.domainSiteId,
                    listingStatus: this.listing.listingStatus
                }
            });
        },
        showBuyerPop() {
            this.createBuyerPop(
                {
                    listing: this.listing,
                    amount: this.listing.buyerAmount,
                    label: this.targetBuyerLabel
                },
                (data) => {
                    this.$emit("updateTargetBuyerCompInfo", data);
                }
            );
        },
        onClick(type) {
            if (this.isMyListingForExpired) {
                return this.handleEditListing();
            }
            if (type == "manageListing") {
                return window.__crmRouter
                    .push({
                        path: "/sales/property/detail",
                        query: {
                            ...this.$route.query,
                            id: this.listing.id
                        }
                    })
                    .catch(() => {});
            }
            if (type == "openDetail") {
                window.open(this.listing.detailUrl);
            }
        },
        getListingPrice(listing) {
            const price = listing.listingStatus == "Sold" ? listing.soldPrice : listing.price;
            if (!price || price.toString() === "-1") {
                return "--";
            }
            if (this.isUK) {
                return formats.pound(price);
            }
            return this.priceFilter(price);
        },
        priceFilter(v = "") {
            v = `${v}`;
            const reg = /(\d)((\d{3})+)$/;
            let match = v.match(reg);
            while (match) {
                v = v.replace(new RegExp(match[0] + "$"), `${match[1]},${match[2]}`);
                match = v.match(reg);
            }
            return `${this.currencySymbol}${v}`;
        },
        getCurCardNums(key) {
            return this.asyncData[key] || "0";
        },
        toolStatus(tool) {
            const { valueKey, name } = tool || {};
            const isBan = ["openHouse", "textCode"].includes(name);
            let isActive = this.asyncData[valueKey];
            if (name === "socialPosts" && !this.hasPostChannelAuth) {
                return "normal";
            }
            if (isActive) {
                return "active";
            }
            if (!isActive && this.isSold && isBan) {
                return "disabled";
            }
            return "normal";
        },
        imgConfig(listing) {
            const { imgDefault, scrollSelector } = this;
            const imgUrl = listing?.previewPicture || imgDefault;
            return {
                value: this.$ft.img_compress(imgUrl, 320),
                errorImg: imgDefault,
                scrollSelector
            };
        },
        goBuyListingPromotion() {
            const _url = this.$router.resolve({
                name: "buyListingPromotion",
                query: {
                    listingId: this.listing.id,
                    mlsOrgId: this.listing.mlsOrgId,
                    step: 1,
                    isPocketListing: this.listing.tags.indexOf("pocket-listing") > -1 ? 1 : 0
                }
            });
            window.open(_url.href, "_blank");
        }
    },
    components: {
        ListingStatusTag
    }
};
</script>

<style lang="less">
.listing-cardview-card {
    max-width: 550px;
    min-width: 370px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid #e1e2e6;
    background-color: #fff;
    box-sizing: border-box;
    .head-img {
        position: relative;
        width: 100%;
        height: 226px;
        cursor: pointer;
        img {
            object-fit: cover;
            width: 100%;
            height: 100%;
        }
        .ad-tag {
            position: absolute;
            top: 0px;
            left: 0px;
            width: 70px;
            height: 70px;
            overflow: hidden;
            border-radius: 4px;
            background: linear-gradient(
                to top left,
                transparent 0%,
                transparent 50%,
                #ffa600 50%,
                #ffa600 100%
            );
            > span {
                position: absolute;
                left: -4px;
                top: 6px;
                color: #fff;
                font-family: Oswald;
                font-size: 9px;
                font-weight: 600;
                line-height: 11px;
                text-transform: uppercase;
                transform: rotate(-45deg);
            }
            &.active {
                background: linear-gradient(
                    to top left,
                    transparent 0%,
                    transparent 50%,
                    var(--primary-color) 50%,
                    var(--primary-color) 100%
                );
            }
        }
        .ad-notice-box {
            display: none;
            .ad-notice {
                position: absolute;
                bottom: 10px;
                left: 10px;
                right: 10px;
                display: flex;
                align-items: center;
                column-gap: 5px;
                background-color: #fff;
                backdrop-filter: blur(2px);
                opacity: 0.9;
                border-radius: 5px;
                padding: 2px 10px;
                color: var(--primary-color);
                font-size: 12px;
                font-weight: 500;
                line-height: 20px;
                .ad-notice-icon {
                    font-size: 14px;
                    transform: rotate(90deg);
                }
                .text {
                    flex: 1;
                }
                &:hover {
                    opacity: 1;
                }
            }
        }
        &:hover {
            .ad-notice-box {
                display: block;
            }
        }
    }
    .tag-box {
        position: absolute;
        top: 0px;
        left: 0px;
        padding: 10px;
        .listing-tag {
            width: 100px;
            height: 24px;
            padding: 5px 10px;
            border-radius: 4px;
            background-color: #202437;
            font-size: 12px;
            line-height: 14px;
            color: #fff;
            margin-bottom: 5px;
            margin-left: 5px;
            opacity: 0.8;
            .icon-success_01 {
                color: #20c472;
            }
            i {
                margin-right: 5px;
            }
            .color-gray {
                color: #a0a3af;
            }
            &.for-sale {
                background-color: #45adff;
            }
            &.to-let {
                background-color: #a021ed;
            }
        }
    }
    .info-box {
        padding: 20px;
        padding-top: 15px;
        .price-line {
            width: 100%;
            box-sizing: border-box;
            font-size: 24px;
            font-weight: bold;
            height: 30px;
            line-height: 30px;
            color: #202437;
            margin-bottom: 10px;
            position: relative;
            padding-right: 50px;
            .listing-status {
                position: absolute;
                top: 0;
                right: 0;
                line-height: 30px;
            }
        }
        .address-line {
            font-size: 14px;
            line-height: 20px;
            height: 20px;
            margin-bottom: 20px;
            color: #797e8b;
            text-overflow: ellipsis;
            max-width: 100%;
            box-sizing: border-box;
            overflow: hidden;
            white-space: nowrap;
            cursor: pointer;
        }
        .tools {
            width: 100%;
            padding-bottom: 20px;
            display: inline-block;
            .icon2017 {
                width: 32px;
                height: 32px;
                box-sizing: border-box;
                margin-right: 10px;
                border-radius: 16px;
                background-color: #f6f7fb;
                line-height: 33px;
                text-align: center;
                display: inline-block;
                vertical-align: bottom;
                color: #a0a3af;
                user-select: none;
                font-size: 16px;
                cursor: pointer;
                &:last-child {
                    margin-right: 0;
                }
            }
            .buyer-compensation {
                display: inline-block;
                margin-left: -4px;
                &.active {
                }
            }
            .tool-hover {
                &:hover,
                &:active,
                &.active {
                    background-color: var(--primary-color);
                    color: #fff;
                }
                &.disabled {
                    cursor: not-allowed;
                    background-color: #f6f7fb !important;
                    color: #a0a3af !important;
                }
            }
        }
        .performance {
            user-select: none;
            padding-bottom: 15px;
            border-bottom: 1px solid #ebecf1;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            .p-item {
                font-size: 14px;
                font-weight: 500;
                color: #797e8b;
                width: 72px;
                height: 24px;
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;
                &:active,
                &:active i,
                &:hover,
                &:hover i {
                    color: var(--primary-color);
                }
                i {
                    color: #a0a3af;
                    margin-right: 5px;
                    font-size: 16px;
                }
            }
            .can-not-click {
                cursor: unset;
            }
        }
        .mng-listing-bt {
            user-select: none;
            cursor: pointer;
            font-size: 14px;
            color: var(--primary-color);
            display: flex;
            width: 100%;
            height: 40px;
            font-weight: 600;
            justify-content: center;
            align-items: center;
            border-radius: 4px;
            background-color: #fff;
            border: 2px solid var(--primary-color);
            box-sizing: border-box;
            &:hover {
                background-color: rgba(var(--primary-color-rgb), 0.1);
            }
            &:active {
                background-color: rgba(var(--primary-color-rgb), 0.3);
            }
        }
    }

    @media screen and (max-width: 2560px) {
        .head-img {
            height: 192px;
        }
    }
    @media screen and (max-width: 1679px) {
        .head-img {
            height: 214px;
        }
    }
    @media screen and (max-width: 1199px) {
        .head-img {
            height: 184px;
        }
    }
    .icon-site_style::before {
        content: "\ea77";
    }
    .icon-openhouse_01::before {
        content: "\e628";
    }
    .icon-textcode_01::before {
        content: "\ea4d";
    }
    .icon-calendar_05::before {
        content: "\e61c";
    }
}
</style>
