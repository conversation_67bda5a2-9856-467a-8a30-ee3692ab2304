<template>
    <div class="property-note-input">
        <noteEditor
            :notClearForm="true"
            :placeholder="placeholder"
            :autofocus="autofocus"
            class="content-input"
            ref="inputHtml"
            :sendContent="sendContent"
            :editorFit="true"
            :placement="placement"
            :id="domId"
            :parentId="domId"
            :option="{
                id: optionId
            }"
            :maxLength="maxLength"
            :isCheckText="isCheckText"
            v-model="dataContent"
        />
    </div>
</template>

<script>
import noteEditor from "@/js/transaction-detail/components/noteEditor.vue";

export default {
    name: "PropertyNoteInput",
    langModule: "listingDetail",

    components: {
        noteEditor
    },

    props: {
        propertyId: {
            type: [String, Number],
            required: true
        },
        optionId: {
            type: String,
            default: "property-note-editor"
        },
        domId: {
            type: String,
            default: "PropertyNoteInput"
        },
        currentType: {
            type: Number,
            default: 16
        },
        showSimple: {
            type: Boolean,
            default: false
        },
        sendContent: {
            type: <PERSON>olean,
            default: false
        },
        placeholder: {
            type: String,
            default: function () {
                try {
                    return this.$t("activities.addNote");
                } catch (error) {
                    return "Add a note...";
                }
            }
        },
        autofocus: {
            type: Boolean,
            default: false
        },
        placement: {
            type: String,
            default: "bottom"
        },
        maxLength: {
            type: Number,
            default: 4000
        },
        isCheckText: {
            type: Boolean,
            default: true
        }
    },

    data() {
        return {
            dataContent: {
                content: "",
                ids: []
            }
        };
    },

    watch: {
        // 父组件点击添加时提取本组件的数据
        sendContent(nv) {
            if (nv) {
                this.$emit("getContent", this.getContent());
            } else {
                this.$refs.inputHtml.innerHTML = "";
            }
        },

        // 点击取消切换输入模式
        showSimple(nv) {
            if (nv) {
                this.$refs.inputHtml.innerHTML = "";
            }
        }
    },

    methods: {
        checkContentLen() {
            return this.$refs.inputHtml.checkContentLen();
        },

        getContent() {
            const content = this.currentType !== 28 
                ? this.dataContent.content 
                : this.dataContent.content.slice(0, 3000);
            
            return {
                content,
                ids: this.dataContent.ids || [],
                idStr: (this.dataContent.ids || []).join(",")
            };
        },

        clearContent() {
            this.dataContent = {
                content: "",
                ids: []
            };
            this.$refs.inputHtml?.resetEditorHeight();
        },

        focus() {
            try {
                this.$refs.inputHtml.editorPromise?.then((res) => {
                    res.focus();
                });
            } catch (error) {
                console.log("Focus error:", error);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.property-note-input {
    flex: 1;
    
    .content-input {
        width: 100%;
        min-height: 100px;
        
        :deep(.editor-container) {
            border: 1px solid #c6c8d1;
            border-radius: 4px;
            background-color: #fff;
            
            &:focus-within {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(93, 81, 226, 0.1);
            }
        }
        
        :deep(.editor-content) {
            padding: 10px;
            min-height: 80px;
            font-size: 14px;
            line-height: 1.5;
            color: #202437;
            
            &:empty:before {
                content: attr(data-placeholder);
                color: #a0a3af;
                pointer-events: none;
            }
        }
    }
}
</style>
