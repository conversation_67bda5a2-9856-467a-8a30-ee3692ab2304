import {
    authority,
    infoData,
    basePermission,
    crmUtils,
    track,
    packageEntry,
    operation as op
} from "crm";
import { globalization } from "common";
import chat from "@/js/common-module/chat/index.js";
import { viewInLoftyWorks } from "@/js/useLoftyWorks";
import { useGlobals } from "@/hooks";

const { isUK } = useGlobals();

let inited = false;
let menuConfig = [];
let rightMenuConfig = [];
let personMenuConfig = [];
let menuInfo = {};
let initCallbackList = [];
const disabledListingDiscover = packageEntry.permission?.disabled?.listingDiscover?.disabled;

/* 
export interface MenuConfig {
    name?: string;
    icon?: string;
    url?: string;
    title?: string;
    type?: string;
    ga?: string;
    show: boolean;
    beta?: boolean;
    redPoint?: boolean | number;
    component?: string;
    active?: false;
    hideAlert?: boolean;
    toObj?: {
        path?;
        name?;
        params?;
        query?;
    };
    clickHandler?: () => unknown;
} */

export function getMenuInfo() {
    const {
        teamType,
        isTeamOwner,
        // packageType,
        // isBrokerAgent,
        listingMgmtWhiteList,
        hasBuiltSite,
        transactionMGMTWhiteList,
        // hasEvaluationSite,
        isCompanyOwner,
        isCompanyAdmin,
        hasCompanySite,
        teamInstance,
        hasBackOffice: BackOffice,
        // hasIdx,
        hasHe,
        isIncrementSite,
        // transactionNoteWhiteList,
        hasBuyAgentWebsite,
        isOfficeAdmin,
        isDepartmentOwner,
        paymentInfo,
        myOwnSite,
        hasOwnSite,
        isCrmOnly, // 用来判断 crm only member 购买 aw, 显示website的入口
        name,
        hasBilling,
        isLofty,
        hasBuyLoftyWorks,
        instanceInfo,
        teamId,
        env,
        companyHasWebsitePkg
    } = infoData.getUserInfo();

    const urlMap = {
        stage: "https://portal.stage.eu.loftyworks.com/admin/home/<USER>/billing",
        production: "https://portal.loftyworks.com/admin/home/<USER>/billing",
        test: "https://portal.test.eu.loftyworks.com/admin/home/<USER>/billing"
    };
    const billingUrl = urlMap[env];
    const hasIdxPackage = infoData.userInfo?.paymentInfo?.hasIdx ?? false;
    // 判断是否是Creator
    const isCreator =
        teamInstance > 0
            ? isCompanyOwner || isDepartmentOwner || isCompanyAdmin || isOfficeAdmin
            : isTeamOwner;

    // 没有hasIdxPackage的套餐的team member，自己没有站需要隐藏website入口 CHIME-15124
    const noHasIdxMember = !hasIdxPackage && !isCreator;
    const noIdxMemberNoSite = noHasIdxMember && !myOwnSite;
    //非idx套餐内：存量未创建he，增量隐藏
    const hideSiteMenu =
        (!hasIdxPackage && !hasHe && !isIncrementSite) ||
        (!hasIdxPackage && isIncrementSite) ||
        noIdxMemberNoSite;
    // 2022-5-05： 开放没有idx的套餐member购买AW入口 CHIME-14933
    const noHasIdx = !paymentInfo?.hasIdx;

    let showAgentWebsite =
        (teamType === 0 &&
            authority.checkUserRight("EDIT_OWN_WEBSITE") &&
            (teamInstance != 1 || isCompanyAdmin || isCompanyOwner || hasCompanySite) &&
            !hideSiteMenu) ||
        ((isCrmOnly || noHasIdx) && !isCreator && hasBuyAgentWebsite);

    // member-（无manage team  website权限）没站 且没有购买AW  隐藏

    const { AgentWebsite, Transaction, dataMigration, Loftywork } = basePermission;

    if (teamInstance === 1) {
        if (
            !AgentWebsite &&
            !hasOwnSite &&
            !hasBuyAgentWebsite &&
            !authority.checkUserRight("MANAGE_TEAM_WEBSITE")
        ) {
            showAgentWebsite = false;
        }
    } else {
        if (
            !AgentWebsite &&
            !hasOwnSite &&
            !hasBuyAgentWebsite &&
            !authority.checkUserRight("MANAGE_TEAM_WEBSITE")
        ) {
            showAgentWebsite = false;
        }
    }
    //  User's Company has not purchased the Website Package, then user cannot see site entrance in Sales CRM
    if (!companyHasWebsitePkg) {
        showAgentWebsite = false;
    }

    return {
        home: {
            icon: "icon-chime_03",
            url: "/admin/home/<USER>",
            toObj: {
                name: "home"
            },
            title: isLofty
                ? globalization.st("common-header", "header.menu.chat")
                : globalization.st("common-header", "header.menu.home"),
            type: "home",
            ga: "Header_Dashboard",
            show: true,
            redPoint: false
        },
        people: {
            icon: "icon-People",
            url: "/admin/home/<USER>/list?type=all",
            toObj: {
                path: "/lead/list",
                query: {
                    type: "all"
                }
            },
            title: globalization.st("common-header", "headerv2.menu.people"),
            type: "people",
            ga: "Header_People",
            show: true,
            redPoint: false
        },

        sales: {
            icon: "icon-sales",
            title: "Sales",
            type: "sales",
            show: true,
            redPoint: false,
            children: [
                {
                    title: "Properties",
                    url: "/admin/home/<USER>",
                    icon: "icon-Listings",
                    toObj: {
                        name: "listingmgmt"
                    },
                    show: true,
                    redPoint: false
                },
                {
                    title: "Key Management",
                    url: "/admin/home/<USER>/key-management",
                    icon: "icon-key",
                    toObj: {
                        path: "/sales/key-management"
                    },
                    show: true,
                    redPoint: false
                }
            ]
        },

        lettings: {
            icon: "icon-lettings",
            title: "Lettings",
            type: "lettings",
            show: true,
            redPoint: false,
            children: [
                {
                    title: "Properties",
                    url: "/admin/home/<USER>",
                    icon: "icon-Listings",
                    toObj: {
                        name: "listingmgmt"
                    },
                    show: true,
                    redPoint: false
                },
                {
                    title: "Key Management",
                    url: "/admin/home/<USER>/key-management",
                    icon: "icon-key",
                    toObj: {
                        path: "/lettings/key-management"
                    },
                    show: true,
                    redPoint: false
                }
            ]
        },

        task: {
            icon: "icon-Calendar",
            url: "/admin/home/<USER>",
            toObj: {
                path: "/task",
                query: {
                    type: "my-all"
                }
            },
            title: isLofty
                ? globalization.st("common-header", "header.menu.showing")
                : globalization.st("common-header", "headerv2.menu.calendar"),
            type: "task",
            ga: "Header_TaskManagement",
            show: true,
            redPoint: false
        },
        reporting: {
            icon: "icon-Reporting",
            url: "/admin/home/<USER>",
            toObj: {
                path: "/reporting"
            },
            title: globalization.st("common-header", "headerv2.menu.reporting"),
            type: "reporting",
            ga: "Header_Report",
            show: teamType !== 2,
            redPoint: false,
            hideAlert: true
        },
        campaign: {
            icon: "icon-Campaign",
            url: "/admin/home/<USER>",
            toObj: {
                name: "campaigns"
            },
            title: globalization.st("common-header", "headerv2.menu.campaign"),
            type: "campaign",
            ga: "Header_campaigns",
            show: teamType !== 2,
            redPoint: false
        },
        // add by lss 2022/03/11 test
        workspace: {
            icon: "icon-inbox",
            title: globalization.st("common-header", "header.menu.workspace"),
            type: "workspace",
            ga: "Header_Workspace",
            beta: true,
            // 放开白名单控制
            // show: teamType !== 2, // lender 无入口 infoData.userInfo.twilioWhiteList,
            show: true, // 4.10需求chat专项下架,
            redPoint: false,
            clickHandler: () => {
                crmUtils.openWindow("/admin/home/<USER>"); // 打开新的tab页面
            }
        },
        site: {
            icon: "icon-Website",
            title: globalization.st("common-header", "headerv2.menu.site"),
            type: "site",
            ga: "Header_Website",
            // todo 临时处理wordpress-blok客户的入口展示，4.25版本重写此处逻辑
            show: showAgentWebsite || [844765721831411, 844433813635847].includes(teamId),
            redPoint: false,
            clickHandler: () => op.goWebsite()
        },
        transaction: {
            icon: "icon-Transaction",
            url: "/admin/home/<USER>",
            toObj: {
                path: "/transaction"
            },
            title: globalization.st("common-header", "headerv2.menu.transaction"),
            type: "transaction",
            ga: "Header_Transactionmgmt",
            show:
                !!Transaction &&
                transactionMGMTWhiteList &&
                authority.checkLimit("_transactionManagement") &&
                teamType === 0,
            redPoint: false
        },
        activity: {
            icon: "icon-activities_04",
            url: "/admin/home/<USER>",
            toObj: {
                path: "/activities",
                query: {
                    type: 1
                }
            },
            type: "activity",
            title: globalization.st("common-header", "header.menu.activity"),
            ga: "Header_Activities",
            show: teamType == 2,
            redPoint: false
        },
        settings: {
            icon: "icon-settings_01",
            url: "/admin/home/<USER>/notification",
            toObj: {
                name: "setting_notification"
            },
            type: "settings",
            title: globalization.st("common-header", "header.menu.settings"),
            ga: "Header_Settings",
            show: true,
            redPoint: false
        },
        accounting: {
            icon: "icon-settings_01",
            url: "/admin/home/<USER>",
            toObj: {
                name: "letting_accounting"
            },
            type: "accounting",
            title: globalization.st("common-header", "header.menu.accounting"),
            ga: "Header_Settings",
            show: true,
            redPoint: false
        },
        // marketplace: {
        //     icon: "icon-Marketplace",
        //     url: "/admin/home/<USER>",
        //     toObj: {
        //         path: "/marketPlace"
        //     },
        //     type: "marketplace",
        //     title: globalization.t("headerv2.menu.marketplace"),
        //     ga: "Header_Marketplace",
        //     show: !!MarketingPlace,
        //     redPoint: false
        // },
        search: {
            icon: "icon-search_01",
            show: true,
            redPoint: false,
            active: false
        },
        noti: {
            icon: "icon-notification_01",
            show: true,
            redPoint: false,
            active: false,
            clickHandler() {
                // TODO: 加到index.vue 中
                track.trackGa.sendEvent({
                    eventCategory: "Notification",
                    eventAction: "Enter Normal Notification"
                });
                chat.getChatNotificationList();
            }
        },
        noti_task: {
            icon: "icon-task_complete",
            show: true,
            redPoint: false,
            active: false,
            clickHandler() {
                track.trackGa.sendEvent({
                    eventCategory: "Notification",
                    eventAction: "Enter Task Reminder"
                });
            }
        },
        noti_oppt: {
            icon: "icon-Oppotunities",
            show: false,
            redPoint: false,
            active: false,
            clickHandler() {
                track.trackGa.sendEvent({
                    eventCategory: "Notification",
                    eventAction: "Enter Opportunities"
                });
            }
        },
        help: {
            component: "Help",
            icon: "icon-help_01",
            show: true,
            redPoint: false,
            active: false
        },
        // 头像dropdown
        profile: {
            icon: "icon-User",
            url: "/admin/home/<USER>/profile",
            toObj: {
                name: "setting_profile"
            },
            title: name,
            show: true
        },
        dataMigration: {
            icon: "icon-continue_writing",
            url: "/admin/home/<USER>",
            title: globalization.st("common-header", "headerv2.user.dataMigration"),
            show: dataMigration
        },
        switchAccount: {
            icon: "icon-a-SwitchAccount",
            url: "",
            title: globalization.st("common-header", "headerv2.user.switch"),
            show: instanceInfo?.length > 1
        },
        loftyWorks: {
            icon: "icon-lofty_02",
            url: "",
            title: globalization.st("common-header", "headerv2.user.loftyWorks"),
            show: Loftywork && hasBuyLoftyWorks,
            clickHandler: () => {
                viewInLoftyWorks();
            }
        },
        setting: {
            icon: "icon-Settings",
            url: "/admin/home/<USER>/profile",
            toObj: {
                name: "setting_profile"
            },
            title: globalization.st("common-header", "headerv2.user.setting"),
            ga: "Header_Settings",
            show: true,
            redPoint: false
        },
        billings: {
            icon: "icon-Billings",
            url: billingUrl,
            title: globalization.st("common-header", "headerv2.user.billing"),
            ga: "Header_Billing",
            show: hasBilling,
            redPoint: false
        },
        update: {
            icon: "icon-a-ProductUpdates",
            url: "https://help.loftyworks.com/hc/en-us/categories/201663123-What-s-New-",
            title: globalization.st("common-header", "headerv2.user.update"),
            ga: "Header_Chime_Updates",
            show: true
        },
        refer: {
            name: "refer",
            icon: "icon-earn_reword",
            url: "/admin/home/<USER>",
            toObj: {
                path: "/referral"
            },
            title: globalization.st("common-header", "headerv2.user.refer"),
            show: false
        },
        backOffice: {
            name: "backOffice",
            icon: "icon-a-BackOffice",
            url: "/backoffice/admin/home",
            title: globalization.st("common-header", "headerv2.user.backoffice"),
            show: BackOffice
        }
    };
}

export function init() {
    if (inited) {
        return;
    }
    inited = true;
    menuInfo = getMenuInfo();
    const getConfig = (arr) => {
        return arr.map((name) => {
            menuInfo[name].name = name;
            return menuInfo[name];
        });
    };

    const { isLofty } = infoData.getUserInfo();
    const crmMenus = [
        "people",
        "sales",
        "lettings",
        "transaction",
        "campaign",
        "task",
        "reporting",
        "activity",
        "site",
        "settings",
        "accounting" // letting accounting
    ];
    const loftyMenus = [
        // "home",
        "people",
        "task"
    ];

    const { LoftyChat } = basePermission;

    if (LoftyChat) {
        loftyMenus.unshift("home");
    }

    // According to the account type, the corresponding main menu directory is displayed.

    const menus = isLofty ? loftyMenus : crmMenus;

    menuConfig = getConfig(menus);

    // right icon
    const rightMenus = ["search", "workspace", "noti_oppt", "noti", "help"];
    rightMenuConfig = getConfig(rightMenus);
    // person dropdown
    const personMenus = [
        "profile",
        // "dataMigration",
        // "switchAccount",
        // "loftyWorks",
        "billings",
        "update"
        // "refer",
        // "backOffice"
    ];
    personMenuConfig = getConfig(personMenus);

    try {
        initCallbackList.map((cb) => {
            cb();
        });
        initCallbackList.length = 0;
    } catch (err) {
        console.log(err);
    }
}

export function getMenuList() {
    init();
    return menuConfig;
}
export function getRightMenuList() {
    init();
    return rightMenuConfig;
}
export function getPersonMenuList() {
    init();
    return personMenuConfig;
}
/**
 * 设置menu配置
 * @param {string} name 需要设置的menu名称
 * @param {string | object} key 需要设置menu配置的键或者以对象形式的键值对
 * @param {any} value 需要设置menu配置的值
 */
/* eslint-disable */
function setMenuConfig(name, key, value) {
    let config = menuInfo[name];
    if (!config) return;
    if (typeof key === "string") {
        config[key] = value;
    } else {
        Object.assign(menuInfo[name], key);
    }
}
function getMenuConfig(name, key) {
    let config = menuInfo[name];
    if (!config) return;
    return config[key];
}

export default {
    setMenuConfig,
    getMenuConfig,
    getRedPoint(name) {
        let _value = getMenuConfig(name, "redPoint");
        if (typeof _value !== "boolean") {
            _value = `${_value}`.replace(/[^\d]/g, "");
        }
        return _value;
    },
    /** 设置menu是否显示 */
    setVisible(name, show) {
        setMenuConfig(name, "show", show);
    },
    /** 设置红泡 */
    setRedPoint(name, show) {
        if (!inited) {
            initCallbackList.push(() => {
                setMenuConfig(name, "redPoint", show);
            });
        } else {
            setMenuConfig(name, "redPoint", show);
        }
    },
    setActive(name, active) {
        setMenuConfig(name, "active", active);
    },
    resetInited() {
        inited = false;
    }
};
