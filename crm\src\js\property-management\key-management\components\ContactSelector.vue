<template>
    <div class="contact-selector">
        <div class="radio-group">
            <RadioBox
                :label="systemLabel"
                :checked="selectionType === 'system'"
                @change="handleTypeChange('system')"
            />
            <RadioBox
                :label="manualLabel"
                :checked="selectionType === 'manual'"
                @change="handleTypeChange('manual')"
            />
        </div>
        <div v-if="selectionType" class="contact-selection">
            <Select
                v-if="selectionType === 'system'"
                :value="systemContactId"
                :dataSource="contactOptions"
                valueMember="id"
                displayMember="name"
                :placeholder="placeholder"
                @datachange="handleSystemContactChange"
            />
            <Input
                v-else
                :value="manualContactName"
                :placeholder="placeholder"
                @input="handleManualContactChange"
            />
        </div>
    </div>
</template>

<script>
import { components } from "common";

const { RadioBox, Select, Input } = components;

export default {
    name: "ContactSelector",
    langModule: "key-management",
    components: {
        RadioBox,
        Select,
        Input
    },
    props: {
        selectionType: {
            type: String,
            default: ""
        },
        systemContactId: {
            type: [String, Number],
            default: ""
        },
        manualContactName: {
            type: String,
            default: ""
        },
        contactOptions: {
            type: Array,
            default: () => []
        },
        systemLabel: {
            type: String,
            default: "System Contact"
        },
        manualLabel: {
            type: String,
            default: "Manual Entry"
        },
        placeholder: {
            type: String,
            default: "Search contact"
        }
    },
    methods: {
        handleTypeChange(type) {
            this.$emit("update:selectionType", type);
            this.$emit("update:systemContactId", "");
            this.$emit("update:manualContactName", "");
        },
        handleSystemContactChange(value) {
            this.$emit("update:systemContactId", value);
        },
        handleManualContactChange(event) {
            this.$emit("update:manualContactName", event.target.value);
        }
    }
};
</script>

<style lang="less" scoped>
.contact-selector {
    .radio-group {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;
    }

    .contact-selection {
        margin-top: 10px;
    }
}
</style>
