<template>
    <PopWin
        :title="dialogTitle"
        :isShow="isShow"
        :width="720"
        :enableDeaultStyle="true"
        :disabledConfirmButton="!isFormValid"
        :confirmText="confirmButtonText"
        :cancelText="$t('dialog.cancel')"
        @close="handleClose"
        @confirm="handleConfirm"
        class="key-dialog"
    >
        <template #content>
            <div class="key-dialog-content">
                <!-- Key Information Row -->
                <div class="key-info-row">
                    <div class="key-image">
                        <img v-if="keyData.photo" :src="keyData.photo" alt="Key photo" />
                        <div v-else class="key-placeholder"></div>
                    </div>
                    <div class="key-details">
                        <div class="key-code">{{ $t('dialog.key_code') }}: {{ keyData.keyCode || 'NERF-UDRF-1234' }}</div>
                        <!-- Check In mode: show current status -->
                        <template v-if="mode === 'checkin'">
                            <div class="divider"></div>
                            <div class="current-status">
                                {{ $t('dialog.current_status', {
                                    holder: keyData.currentHolder || 'John <PERSON>man',
                                    reason: keyData.checkoutReason || 'Viewing'
                                }) }}
                            </div>
                            <div class="divider"></div>
                            <div class="expected-return">
                                {{ $t('dialog.expected_return') }}: {{ keyData.expectedReturnDate || '02 Aug 2024' }}
                            </div>
                        </template>
                    </div>
                </div>

                <Form ref="formRef" :model="formData" :rules="formRules" labelPosition="top">
                    <!-- Check Out Fields -->
                    <template v-if="mode === 'checkout'">
                        <!-- Check Out Of -->
                        <FormItem :label="$t('dialog.fields.check_out_of')" prop="checkoutType">
                            <template #default="{ changeHandler }">
                                <div class="radio-group">
                                    <RadioBox
                                        :label="$t('dialog.fields.system_contact')"
                                        :checked="formData.checkoutType === 'system'"
                                        @change="handleCheckoutTypeChange('system', changeHandler)"
                                    />
                                    <RadioBox
                                        :label="$t('dialog.fields.manual_entry')"
                                        :checked="formData.checkoutType === 'manual'"
                                        @change="handleCheckoutTypeChange('manual', changeHandler)"
                                    />
                                </div>
                                <div v-if="formData.checkoutType" class="contact-selection">
                                    <Select
                                        v-if="formData.checkoutType === 'system'"
                                        v-model="formData.contactId"
                                        :dataSource="contactOptions"
                                        valueMember="id"
                                        displayMember="name"
                                        :placeholder="$t('dialog.fields.search_contact')"
                                        @datachange="handleContactChange"
                                    />
                                    <Input
                                        v-else
                                        v-model="formData.contactName"
                                        :placeholder="$t('dialog.fields.search_contact')"
                                    />
                                </div>
                            </template>
                        </FormItem>

                        <!-- Reason for check out -->
                        <FormItem :label="$t('dialog.fields.checkout_reason')" prop="reason">
                            <Select
                                v-model="formData.reason"
                                :dataSource="reasonOptions"
                                valueMember="value"
                                displayMember="label"
                                :placeholder="$t('dialog.fields.select_reason')"
                                @datachange="handleReasonChange"
                            />
                        </FormItem>

                        <!-- Expected return date -->
                        <FormItem :label="$t('dialog.fields.expected_return_date')" prop="expectedReturnDate">
                            <SelectDatePicker
                                v-model="formData.expectedReturnDate"
                                :placeholder="$t('dialog.fields.select_date')"
                                @pick="handleDatePick"
                            />
                        </FormItem>

                        <!-- Note -->
                        <FormItem :label="$t('dialog.fields.note')" prop="note">
                            <textarea
                                v-model="formData.note"
                                class="note-textarea"
                                :placeholder="$t('dialog.fields.checkout_note_placeholder')"
                                rows="5"
                            ></textarea>
                        </FormItem>
                    </template>

                    <!-- Check In Fields -->
                    <template v-if="mode === 'checkin'">
                        <!-- Return Date -->
                        <FormItem :label="$t('dialog.fields.return_date')" prop="returnDate">
                            <SelectDatePicker
                                v-model="formData.returnDate"
                                :placeholder="$t('dialog.fields.select_date')"
                                @pick="handleReturnDatePick"
                            />
                        </FormItem>

                        <!-- Return by -->
                        <FormItem :label="$t('dialog.fields.return_by')" prop="returnByType">
                            <div class="radio-group">
                                <RadioBox
                                    :label="$t('dialog.fields.system_contact')"
                                    :checked="formData.returnByType === 'system'"
                                    @change="handleReturnByTypeChange('system')"
                                />
                                <RadioBox
                                    :label="$t('dialog.fields.manual_entry')"
                                    :checked="formData.returnByType === 'manual'"
                                    @change="handleReturnByTypeChange('manual')"
                                />
                            </div>
                            <div v-if="formData.returnByType" class="contact-selection">
                                <Select
                                    v-if="formData.returnByType === 'system'"
                                    v-model="formData.returnByContactId"
                                    :dataSource="contactOptions"
                                    valueMember="id"
                                    displayMember="name"
                                    :placeholder="$t('dialog.fields.search_contact')"
                                    @datachange="handleReturnByContactChange"
                                />
                                <Input
                                    v-else
                                    v-model="formData.returnByContactName"
                                    :placeholder="$t('dialog.fields.search_contact')"
                                />
                            </div>
                        </FormItem>

                        <!-- Storage Location -->
                        <FormItem :label="$t('dialog.fields.storage_location')" prop="storageLocation">
                            <Input
                                v-model="formData.storageLocation"
                                :placeholder="$t('dialog.fields.select_storage_location')"
                            />
                        </FormItem>

                        <!-- Current Holder -->
                        <FormItem :label="$t('dialog.fields.current_holder')" prop="currentHolderType">
                            <div class="radio-group">
                                <RadioBox
                                    :label="$t('dialog.fields.system_contact')"
                                    :checked="formData.currentHolderType === 'system'"
                                    @change="handleCurrentHolderTypeChange('system')"
                                />
                                <RadioBox
                                    :label="$t('dialog.fields.manual_entry')"
                                    :checked="formData.currentHolderType === 'manual'"
                                    @change="handleCurrentHolderTypeChange('manual')"
                                />
                            </div>
                            <div v-if="formData.currentHolderType" class="contact-selection">
                                <Select
                                    v-if="formData.currentHolderType === 'system'"
                                    v-model="formData.currentHolderContactId"
                                    :dataSource="contactOptions"
                                    valueMember="id"
                                    displayMember="name"
                                    :placeholder="$t('dialog.fields.search_contact')"
                                    @datachange="handleCurrentHolderContactChange"
                                />
                                <Input
                                    v-else
                                    v-model="formData.currentHolderContactName"
                                    :placeholder="$t('dialog.fields.search_contact')"
                                />
                            </div>
                        </FormItem>

                        <!-- Note -->
                        <FormItem :label="$t('dialog.fields.note')" prop="note">
                            <textarea
                                v-model="formData.note"
                                class="note-textarea"
                                :placeholder="$t('dialog.fields.checkin_note_placeholder')"
                                rows="5"
                            ></textarea>
                        </FormItem>
                    </template>
                </Form>
            </div>
        </template>
    </PopWin>
</template>

<script>
import { components } from "common";

const { PopWin, Form, FormItem, Input, Select, RadioBox, SelectDatePicker } = components;

export default {
    name: "KeyDialog",
    langModule: "key-management",
    components: {
        PopWin,
        Form,
        FormItem,
        Input,
        Select,
        RadioBox,
        SelectDatePicker
    },
    props: {
        isShow: {
            type: Boolean,
            default: false
        },
        mode: {
            type: String,
            default: "checkout", // 'checkout' or 'checkin'
            validator: (value) => ["checkout", "checkin"].includes(value)
        },
        keyData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            formData: {
                // Checkout fields
                checkoutType: "", // 'system' or 'manual'
                contactId: "",
                contactName: "",
                reason: "",
                expectedReturnDate: "",
                note: "",

                // Checkin fields
                returnDate: "",
                returnByType: "", // 'system' or 'manual'
                returnByContactId: "",
                returnByContactName: "",
                storageLocation: "",
                currentHolderType: "", // 'system' or 'manual'
                currentHolderContactId: "",
                currentHolderContactName: ""
            },
            contactOptions: [
                { id: 1, name: "Ralph Edwards" },
                { id: 2, name: "Eleanor Pena" },
                { id: 3, name: "John Doe" }
            ],
            reasonOptions: [
                { value: "viewing", label: this.$t('reason_options.viewing') },
                { value: "inspection", label: this.$t('reason_options.inspection') },
                { value: "maintenance", label: this.$t('reason_options.maintenance') },
                { value: "showing", label: this.$t('reason_options.showing') },
                { value: "other", label: this.$t('reason_options.other') }
            ]
        };
    },
    computed: {
        dialogTitle() {
            return this.mode === "checkout"
                ? this.$t("dialog.checkout.title")
                : this.$t("dialog.checkin.title");
        },
        confirmButtonText() {
            return this.mode === "checkout"
                ? this.$t("dialog.checkout.confirm_button")
                : this.$t("dialog.checkin.confirm_button");
        },
        formRules() {
            const rules = {};

            if (this.mode === "checkout") {
                rules.checkoutType = [
                    { required: true, message: this.$t("dialog.validation.checkout_type_required"), trigger: "change" }
                ];
                rules.reason = [
                    { required: true, message: this.$t("dialog.validation.reason_required"), trigger: "change" }
                ];
                rules.expectedReturnDate = [
                    { required: true, message: this.$t("dialog.validation.expected_return_date_required"), trigger: "change" }
                ];
            } else if (this.mode === "checkin") {
                rules.returnDate = [
                    { required: true, message: this.$t("dialog.validation.return_date_required"), trigger: "change" }
                ];
                rules.returnByType = [
                    { required: true, message: this.$t("dialog.validation.return_by_type_required"), trigger: "change" }
                ];
                rules.storageLocation = [
                    { required: true, message: this.$t("dialog.validation.storage_location_required"), trigger: "blur" }
                ];
                rules.currentHolderType = [
                    { required: true, message: this.$t("dialog.validation.current_holder_type_required"), trigger: "change" }
                ];
            }

            return rules;
        },
        isFormValid() {
            if (this.mode === "checkout") {
                return this.formData.checkoutType &&
                       this.formData.reason &&
                       this.formData.expectedReturnDate &&
                       (this.formData.checkoutType === "system" ? this.formData.contactId : this.formData.contactName);
            } else if (this.mode === "checkin") {
                return this.formData.returnDate &&
                       this.formData.returnByType &&
                       this.formData.storageLocation &&
                       this.formData.currentHolderType &&
                       (this.formData.returnByType === "system" ? this.formData.returnByContactId : this.formData.returnByContactName) &&
                       (this.formData.currentHolderType === "system" ? this.formData.currentHolderContactId : this.formData.currentHolderContactName);
            }
            return false;
        }
    },
    watch: {
        isShow(newVal) {
            if (newVal) {
                this.initFormData();
            }
        },
        mode() {
            this.initFormData();
        },
        keyData: {
            handler(newData) {
                if (newData && Object.keys(newData).length > 0) {
                    this.initFormData();
                }
            },
            deep: true,
            immediate: true
        }
    },
    methods: {
        initFormData() {
            this.resetForm();

            // Pre-fill data based on mode and keyData
            if (this.mode === "checkin" && this.keyData) {
                // For check-in, pre-fill with current key holder info
                this.formData.returnDate = new Date().toISOString().split('T')[0];
                this.formData.storageLocation = this.keyData.storageLocation || "";
                this.formData.currentHolderType = this.keyData.holderType || "system";
                this.formData.currentHolderContactId = this.keyData.holderId || "";
                this.formData.currentHolderContactName = this.keyData.holderName || "";
            } else if (this.mode === "checkout" && this.keyData) {
                // For check-out, set default expected return date to tomorrow
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                this.formData.expectedReturnDate = tomorrow.toISOString().split('T')[0];
            }
        },
        resetForm() {
            this.formData = {
                // Checkout fields
                checkoutType: "",
                contactId: "",
                contactName: "",
                reason: "",
                expectedReturnDate: "",
                note: "",

                // Checkin fields
                returnDate: "",
                returnByType: "",
                returnByContactId: "",
                returnByContactName: "",
                storageLocation: "",
                currentHolderType: "",
                currentHolderContactId: "",
                currentHolderContactName: ""
            };
            this.$nextTick(() => {
                if (this.$refs.formRef) {
                    this.$refs.formRef.clearValidate();
                }
            });
        },
        // Checkout event handlers
        handleCheckoutTypeChange(type, changeHandler) {
            if (this.formData.checkoutType !== type) {
                this.formData.checkoutType = type;
                this.formData.contactId = "";
                this.formData.contactName = "";
                if (changeHandler) {
                    changeHandler();
                }
            }
        },
        handleContactChange(value) {
            this.formData.contactId = value;
        },
        handleReasonChange(value) {
            this.formData.reason = value;
        },
        handleDatePick(date) {
            this.formData.expectedReturnDate = date;
        },

        // Checkin event handlers
        handleReturnDatePick(date) {
            this.formData.returnDate = date;
        },
        handleReturnByTypeChange(type) {
            if (this.formData.returnByType !== type) {
                this.formData.returnByType = type;
                this.formData.returnByContactId = "";
                this.formData.returnByContactName = "";
            }
        },
        handleReturnByContactChange(value) {
            this.formData.returnByContactId = value;
        },
        handleCurrentHolderTypeChange(type) {
            if (this.formData.currentHolderType !== type) {
                this.formData.currentHolderType = type;
                this.formData.currentHolderContactId = "";
                this.formData.currentHolderContactName = "";
            }
        },
        handleCurrentHolderContactChange(value) {
            this.formData.currentHolderContactId = value;
        },
        validateField(field) {
            if (this.$refs.formRef) {
                this.$refs.formRef.validateField(field);
            }
        },
        handleClose() {
            this.$emit("close");
        },
        async handleConfirm() {
            try {
                const valid = await this.$refs.formRef.validate();
                if (valid) {
                    const formData = {
                        ...this.formData,
                        mode: this.mode,
                        keyId: this.keyData.id
                    };

                    // 处理联系人信息
                    if (this.mode === "checkout") {
                        if (formData.checkoutType === "system" && formData.contactId) {
                            const contact = this.contactOptions.find(c => c.id === formData.contactId);
                            formData.contactName = contact ? contact.name : "";
                        }
                    } else if (this.mode === "checkin") {
                        if (formData.returnByType === "system" && formData.returnByContactId) {
                            const contact = this.contactOptions.find(c => c.id === formData.returnByContactId);
                            formData.returnByContactName = contact ? contact.name : "";
                        }
                        if (formData.currentHolderType === "system" && formData.currentHolderContactId) {
                            const contact = this.contactOptions.find(c => c.id === formData.currentHolderContactId);
                            formData.currentHolderContactName = contact ? contact.name : "";
                        }
                    }

                    this.$emit("confirm", formData);
                }
            } catch (error) {
                console.error("Form validation failed:", error);
            }
        }
    }
};
</script>

<style lang="less" scoped>
.key-dialog {
    :deep(.pop-container) {
        border-radius: 6px;
        box-shadow: 0px 5px 10px 0px rgba(0, 0, 0, 0.1);
    }

    :deep(.pop-title) {
        padding: 12px 15px 12px 20px;
        border-bottom: 1px solid #EBECF1;

        .title {
            font-family: SF Pro;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.625em;
            text-transform: uppercase;
            color: #202437;
        }
    }

    :deep(.pop-body) {
        padding: 20px 30px;
    }

    :deep(.pop-footer__default) {
        padding: 20px 30px;
        border-top: 1px solid #EBECF1;
        display: flex;
        justify-content: flex-end;
        gap: 10px;

        .chime-btn {
            padding: 10px 20px;
            width: 100px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 400;

            &.primary {
                background-color: #5D51E2;
                color: #FFFFFF;
            }

            &.invisible {
                background-color: transparent;
                color: #797E8B;
            }
        }
    }
}

.key-dialog-content {
    .key-info-row {
        display: flex;
        align-items: flex-start;
        gap: 15px;
        padding: 12px;
        border: 1px solid #EBECF1;
        border-radius: 12px;
        margin-bottom: 20px;

        .key-image {
            width: 120px;
            height: 120px;
            flex-shrink: 0;
            border-radius: 6px;
            overflow: hidden;
            background-color: #F5F5F5;

            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }

            .key-placeholder {
                width: 100%;
                height: 100%;
                background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjEyMCIgdmlld0JveD0iMCAwIDEyMCAxMjAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjYwIiB5PSI2NSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE0IiBmaWxsPSIjQzZDOEQxIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5LZXkgSW1hZ2U8L3RleHQ+Cjwvc3ZnPgo=') center/cover;
            }
        }

        .key-details {
            flex: 1;
            padding: 10px 0;

            .key-code {
                font-family: SF Pro;
                font-weight: 400;
                font-size: 14px;
                line-height: 1.4285714286em;
                color: #797E8B;
                margin-bottom: 10px;
            }

            .divider {
                height: 1px;
                background: #EBECF1;
                margin: 10px 0;
                border: none;
                background-image: repeating-linear-gradient(
                    to right,
                    #EBECF1,
                    #EBECF1 4px,
                    transparent 4px,
                    transparent 8px
                );
            }

            .current-status,
            .expected-return {
                font-family: SF Pro;
                font-weight: 400;
                font-size: 14px;
                line-height: 1.4285714286em;
                color: #797E8B;
                margin-bottom: 10px;
            }
        }
    }

    .radio-group {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;
    }

    .contact-selection {
        margin-top: 10px;
    }

    .note-textarea {
        width: 100%;
        min-height: 120px;
        padding: 10px;
        border: 1px solid #C6C8D1;
        border-radius: 6px;
        font-family: SF Pro;
        font-size: 14px;
        color: #202437;
        resize: vertical;

        &::placeholder {
            color: #C6C8D1;
        }

        &:focus {
            outline: none;
            border-color: #5D51E2;
        }
    }

    :deep(.com-form) {
        .com-form-item {
            margin-bottom: 20px;

            .form-item-label {
                font-family: SF Pro;
                font-weight: 700;
                font-size: 14px;
                line-height: 1.4285714286em;
                color: #515666;
                margin-bottom: 10px;
            }

            .chime-input {
                height: 40px;
                border: 1px solid #C6C8D1;
                border-radius: 6px;
                padding: 0 10px;
                font-size: 14px;
                color: #202437;

                &::placeholder {
                    color: #C6C8D1;
                }
            }

            .com-select {
                .select-input {
                    height: 40px;
                    border: 1px solid #C6C8D1;
                    border-radius: 6px;

                    .select-placeholder {
                        color: #C6C8D1;
                    }
                }
            }
        }
    }
}
</style>
