<template>
    <div class="contact-info" v-if="contact">
        <div class="info-item" v-if="contact.name">
            <i class="icon2017 icon-people_01"></i>
            <span>{{ contact.name }}</span>
        </div>
        <div class="info-item" v-if="contact.email || contact.phone">
            <i class="icon2017" :class="contact.email ? 'icon-mail' : 'icon-call_01'"></i>
            <span>{{ contact.email || contact.phone }}</span>
        </div>
        <div class="info-item" v-if="contact.company">
            <i class="icon2017 icon-building_01"></i>
            <span>{{ contact.company }}</span>
        </div>
    </div>
</template>

<script>
export default {
    name: "ContactInfo",
    langModule: "key-management",
    props: {
        contact: {
            type: Object,
            default: null
        },
        layout: {
            type: String,
            default: "vertical", // vertical | horizontal
            validator: (value) => ["vertical", "horizontal"].includes(value)
        }
    }
};
</script>

<style lang="less" scoped>
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 5px;

    &.horizontal {
        flex-direction: row;
        gap: 15px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
            width: 12px;
            height: 12px;
            color: #515666;
            font-size: 12px;
            flex-shrink: 0;
        }

        span {
            font-family: SF Pro;
            font-weight: 400;
            font-size: 12px;
            line-height: 1.67em;
            color: #515666;
        }
    }
}
</style>
