import { utils } from "common";

/**
 * Property Management Detail API
 * 处理房产管理详情页面相关的API调用
 */
export default {
    /**
     * 添加房产笔记
     * @param {Object} params - 笔记参数
     * @param {string} params.content - 笔记内容
     * @param {number} params.type - 笔记类型 (16: 笔记, 25: 通话记录, 7: 邮件记录)
     * @param {string|number} params.propertyId - 房产ID
     * @param {boolean} params.pinToTop - 是否置顶
     * @param {number} params.timestamp - 时间戳
     * @returns {Promise} API响应
     */
    addPropertyNote(params) {
        return utils.ajax({
            url: "/listing-hub/property/note/add",
            type: "POST",
            contentType: "application/json",
            data: JSON.stringify(params),
            errorTip: true
        });
    },

    /**
     * 获取房产时间线数据
     * @param {string|number} propertyId - 房产ID
     * @param {Object} options - 查询选项
     * @param {number} options.page - 页码
     * @param {number} options.pageSize - 每页数量
     * @param {string} options.type - 类型筛选
     * @returns {Promise} API响应
     */
    getPropertyTimeline(propertyId, options = {}) {
        const { page = 1, pageSize = 20, type = "all" } = options;
        
        return utils.ajax({
            url: `/listing-hub/property/${propertyId}/timeline`,
            type: "GET",
            data: {
                page,
                pageSize,
                type
            }
        });
    },

    /**
     * 更新房产笔记
     * @param {string|number} noteId - 笔记ID
     * @param {Object} params - 更新参数
     * @returns {Promise} API响应
     */
    updatePropertyNote(noteId, params) {
        return utils.ajax({
            url: `/listing-hub/property/note/${noteId}`,
            type: "PUT",
            contentType: "application/json",
            data: JSON.stringify(params),
            errorTip: true
        });
    },

    /**
     * 删除房产笔记
     * @param {string|number} noteId - 笔记ID
     * @returns {Promise} API响应
     */
    deletePropertyNote(noteId) {
        return utils.ajax({
            url: `/listing-hub/property/note/${noteId}`,
            type: "DELETE",
            errorTip: true
        });
    },

    /**
     * 获取房产详情
     * @param {string|number} propertyId - 房产ID
     * @returns {Promise} API响应
     */
    getPropertyDetail(propertyId) {
        return utils.ajax({
            url: `/listing-hub/property/${propertyId}`,
            type: "GET"
        });
    },

    /**
     * 获取房产活动统计
     * @param {string|number} propertyId - 房产ID
     * @returns {Promise} API响应
     */
    getPropertyActivityStats(propertyId) {
        return utils.ajax({
            url: `/listing-hub/property/${propertyId}/activity-stats`,
            type: "GET"
        });
    }
};
