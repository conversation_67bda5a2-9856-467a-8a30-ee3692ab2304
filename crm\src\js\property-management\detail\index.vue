
<template>
    <div class="property-detail">
        <Side />
        <TabBar :activeTab="activeTab" @tab-change="handleTabChange" />
        <NotePanel :propertyId="propertyId" @note-added="onNoteAdded" />
        <div class="main-content">
            <!-- Overview Tab -->
            <template v-if="activeTab === 'overview'">
                <Timeline />
                <Tasks />
            </template>

            <!-- Keys Tab -->
            <template v-else-if="activeTab === 'keys'">
                <KeyManagement />
            </template>

            <!-- Other tabs placeholder -->
            <template v-else>
                <div class="tab-placeholder">
                    <h3>{{ getTabLabel(activeTab) }}</h3>
                    <p>This tab content is coming soon...</p>
                </div>
            </template>
        </div>
    </div>
</template>

<script>
import TabBar from "./components/TabBar.vue";
import Side from "./components/Side.vue";
import Timeline from "./components/Timeline.vue";
import Tasks from "./components/Tasks.vue";
import NotePanel from "./components/NotePanel.vue";
import KeyManagement from "../key-management/index.vue";

export default {
    name: "PropertyDetail",
    langModule: "listingDetail",
    components: {
        TabBar,
        Side,
        Timeline,
        Tasks,
        NotePanel,
        KeyManagement
    },
    data() {
        return {
            activeTab: "overview",
            propertyId: this.$route.params.id || "default-property-id"
        };
    },
    methods: {
        handleTabChange(tabKey) {
            this.activeTab = tabKey;
        },
        getTabLabel(tabKey) {
            const tabLabels = {
                overview: "Overview",
                marketing: "Marketing",
                keys: "Keys",
                viewings: "Viewings",
                buyers: "Buyers",
                offers: "Offers",
                "sales-progression": "Sales Progression",
                more: "More"
            };
            return tabLabels[tabKey] || tabKey;
        },

        onNoteAdded(noteData) {
            // 处理笔记添加成功后的逻辑
            console.log("Note added:", noteData);

            // 通知 Timeline 组件刷新数据
            if (this.$refs.timeline) {
                this.$refs.timeline.refreshTimeline();
            }

            // 可以在这里添加其他需要的逻辑，比如更新统计数据等
        }
    }
};
</script>

<style scoped>
.property-detail {
    display: grid;
    grid-template-columns: auto 1fr 1fr;
    grid-template-rows: auto auto 1fr;
    grid-template-areas:
        "side tab-bar tab-bar"
        "side note-panel note-panel"
        "side main-content main-content";
    height: 100vh;
    gap: 16px;
}

.property-detail > * {
    min-width: 0;
}

.property-detail > :nth-child(1) {
    /* Side */
    grid-area: side;
}

.property-detail > :nth-child(2) {
    /* TabBar */
    grid-area: tab-bar;
}

.property-detail > :nth-child(3) {
    /* NotePanel */
    grid-area: note-panel;
}

.property-detail > :nth-child(4) {
    /* main-content */
    grid-area: main-content;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.tab-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #797e8b;
    text-align: center;
}

.tab-placeholder h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #202437;
}

.tab-placeholder p {
    font-size: 16px;
    margin: 0;
}
</style>
