<template>
    <div class="info-card" :class="cardClass">
        <!-- Header Slot -->
        <div class="card-header" v-if="$slots.header">
            <slot name="header"></slot>
        </div>

        <!-- Content Slot -->
        <div class="card-content">
            <slot name="content"></slot>
        </div>

        <!-- Footer Slot -->
        <div class="card-footer" v-if="$slots.footer">
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script>
export default {
    name: "InfoCard",
    langModule: "key-management",
    props: {
        cardClass: {
            type: String,
            default: ""
        },
        width: {
            type: String,
            default: "480px"
        },
        padding: {
            type: String,
            default: "0 20px"
        }
    }
};
</script>

<style lang="less" scoped>
.info-card {
    background: #ffffff;
    border: 1px solid #ebecf1;
    border-radius: 12px;
    padding: v-bind(padding);
    width: v-bind(width);
    box-sizing: border-box;

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0;
    }

    .card-content {
        flex: 1;
    }

    .card-footer {
        display: flex;
        justify-content: stretch;
        align-items: stretch;
        align-self: stretch;
        gap: 10px;
        padding: 10px 0;
    }
}
</style>
